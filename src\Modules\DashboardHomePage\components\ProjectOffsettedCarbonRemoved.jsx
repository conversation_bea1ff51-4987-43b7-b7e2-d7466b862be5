import React from 'react';
import { projectsData, projectCategories, dashboardConfig } from '../data/mockData';

const ProjectOffsettedCarbonRemoved = () => {
  const hasData = dashboardConfig.hasProjectData;

  if (!hasData) {
    // Empty state - show categories only
    return (
      <section className="p-8">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-2xl font-bold text-[#003B2D]">Project Offsetted &Carbon Removed</h2>
          <button className="text-[#003B2D] border border-gray-300 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
            Export PDF
          </button>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          {projectCategories.map((category, index) => (
            <div key={index} className={`${category.color} p-6 rounded-lg text-center border-2 border-dashed border-gray-300`}>
              <h3 className="font-semibold text-[#003B2D] mb-2">{category.name}</h3>
              <button className="text-[#10B981] text-sm hover:underline">
                {category.count}
              </button>
            </div>
          ))}
        </div>
      </section>
    );
  }

  return (
    <section className="p-8">
      <div className="flex justify-between items-center mb-8">
        <h2 className="text-2xl font-bold text-[#003B2D]">Project Offsetted &Carbon Removed</h2>
        <button className="text-[#003B2D] border border-gray-300 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
          Export PDF
        </button>
      </div>
      
      {/* Project Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {projectsData.map((project) => (
          <div key={project.id} className="rounded-lg overflow-hidden shadow-sm">
            <div className="relative h-48 bg-gradient-to-br from-green-400 to-green-600">
              {/* Project image placeholder */}
              <div className="absolute inset-0 bg-black bg-opacity-20"></div>
              <div className="absolute bottom-4 left-4 text-white">
                <div className="flex items-center gap-2 mb-2">
                  <span className="w-2 h-2 bg-white rounded-full"></span>
                  <span className="text-sm">{project.type}</span>
                </div>
                <h3 className="font-bold text-lg">{project.title}</h3>
                <p className="text-sm">{project.year}</p>
              </div>
              <div className="absolute top-4 right-4">
                <span className="bg-white bg-opacity-20 text-white px-2 py-1 rounded text-xs">
                  {project.location}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Categories */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <div className="bg-green-100 p-4 rounded-lg text-center">
          <h3 className="font-semibold text-[#003B2D] mb-2">Social Justice</h3>
          <button className="text-[#10B981] text-sm hover:underline">
            See Projects
          </button>
        </div>
        <div className="bg-green-100 p-4 rounded-lg text-center">
          <h3 className="font-semibold text-[#003B2D] mb-2">Education</h3>
          <button className="text-[#10B981] text-sm hover:underline">
            See Projects
          </button>
        </div>
      </div>
    </section>
  );
};

export default ProjectOffsettedCarbonRemoved;
