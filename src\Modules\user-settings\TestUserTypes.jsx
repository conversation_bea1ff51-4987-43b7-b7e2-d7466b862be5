import React, { useState } from 'react';
import EditProfile from './EditProfile';

const TestUserTypes = () => {
  const [userType, setUserType] = useState('company');

  return (
    <div>
      <div style={{ position: 'fixed', top: '10px', right: '10px', zIndex: 1000, background: 'white', padding: '10px', border: '1px solid #ccc' }}>
        <h3>Test User Types</h3>
        <button 
          onClick={() => setUserType('company')}
          style={{ 
            margin: '5px', 
            padding: '5px 10px', 
            backgroundColor: userType === 'company' ? '#81F18E' : '#f0f0f0' 
          }}
        >
          Company User
        </button>
        <button 
          onClick={() => setUserType('individual')}
          style={{ 
            margin: '5px', 
            padding: '5px 10px', 
            backgroundColor: userType === 'individual' ? '#81F18E' : '#f0f0f0' 
          }}
        >
          Individual User
        </button>
      </div>
      <EditProfile key={userType} userType={userType} />
    </div>
  );
};

export default TestUserTypes;
