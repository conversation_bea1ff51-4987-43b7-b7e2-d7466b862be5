// Mock data for dashboard components
// This will be replaced with API calls later

export const carbonEmissionData = {
  value: 35.3,
  breakdown: [
    { label: 'Gas', value: 40, color: '#004D40' },
    { label: 'Car', value: 35, color: '#81F18E' },
    { label: 'Travel', value: 25, color: '#D6EFD7' }
  ]
};

export const offsetData = {
  value: 35.3,
  offsetted: '12/22 Tons are offsetted',
  percentage: 60 // 60% offsetted
};

export const projectsData = [
  {
    id: 1,
    title: 'Fuel Efficient Stoves',
    type: 'Carbon Offsetting',
    location: 'Egypt',
    year: 'SAR 2015',
    image: '/api/placeholder/300/200',
    category: 'Clean Water',
    flag: '🇪🇬'
  },
  {
    id: 2,
    title: 'Safe Water Project',
    type: 'Reforestation',
    location: 'Brazil',
    year: 'SAR 2019',
    image: '/api/placeholder/300/200',
    category: 'Ocean Plastic',
    flag: '🇧🇷'
  },
  {
    id: 3,
    title: 'Catalytic N2O destruction',
    type: 'Reforestation',
    location: 'Netherlands',
    year: 'SAR 2015',
    image: '/api/placeholder/300/200',
    category: 'Reforestation',
    flag: '🇳🇱'
  },
  {
    id: 4,
    title: 'Fuel Efficient Stoves',
    type: 'Carbon Offsetting',
    location: 'Egypt',
    year: 'SAR 2015',
    image: '/api/placeholder/300/200',
    category: 'Social Justice',
    flag: '🇪🇬'
  }
];

export const projectCategories = [
  { name: 'Clean Water', count: 'See Projects', color: 'bg-green-100' },
  { name: 'Ocean Plastic', count: 'See Projects', color: 'bg-green-100' },
  { name: 'Reforestation', count: 'See Projects', color: 'bg-green-100' },
  { name: 'Social Justice', count: 'See Projects', color: 'bg-green-100' },
  { name: 'Education', count: 'See Projects', color: 'bg-green-100' }
];

export const offsetHistoryData = [
  {
    orderId: '156163',
    type: 'Carbon offset',
    partners: 'Akhdar',
    projects: 'Projects',
    from: 'Egypt',
    price: '2015 SAR',
    impact: 'Offsetting',
    createdAt: '10/5/2025'
  },
  {
    orderId: '156163',
    type: 'Carbon offset',
    partners: 'Akhdar',
    projects: 'Projects',
    from: 'Egypt',
    price: '2015 SAR',
    impact: 'Offsetting',
    createdAt: '10/5/2025'
  },
  {
    orderId: '156163',
    type: 'Carbon offset',
    partners: 'Akhdar',
    projects: 'Projects',
    from: 'Egypt',
    price: '2015 SAR',
    impact: 'Offsetting',
    createdAt: '10/5/2025'
  }
];

export const subscriptionsData = [
  {
    project: 'Fuel Efficient Stoves',
    type: 'Carbon Offset',
    price: '2015 SAR/Month'
  },
  {
    project: 'Fuel Efficient Stoves',
    type: 'Carbon Offset',
    price: '2015 SAR/Month'
  },
  {
    project: 'Fuel Efficient Stoves',
    type: 'Carbon Offset',
    price: '2015 SAR/Month'
  }
];

// Configuration for toggling between empty and populated states
export const dashboardConfig = {
  hasCalculationData: true,
  hasProjectData: true,
  hasLocationData: true,
  hasHistoryData: true
};
