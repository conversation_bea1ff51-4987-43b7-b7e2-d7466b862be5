import React, { useState } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Input } from '../../components/ui/Input';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '../../components/ui/select';
import { Eye, EyeOff } from 'lucide-react';
import countryList from 'country-list';
import { toast, Toaster } from "sonner";
import ErrorMessage from '../../components/ErrorMessage';
import { useTranslation } from 'react-i18next';
import SettingsLayout from './components/SettingsLayout';

// Industry options (reused from CompanySignUpForm)
const industries = [
  { value: 'technology', label: 'Technology' },
  { value: 'finance', label: 'Finance' },
  { value: 'healthcare', label: 'Healthcare' },
  { value: 'education', label: 'Education' },
  { value: 'retail', label: 'Retail' },
  { value: 'manufacturing', label: 'Manufacturing' },
  { value: 'consulting', label: 'Consulting' },
  { value: 'other', label: 'Other' },
];

// Company size options (reused from CompanySignUpForm)
const companySizes = [
  { value: '1-10', label: '1-10 employees' },
  { value: '11-50', label: '11-50 employees' },
  { value: '51-200', label: '51-200 employees' },
  { value: '201-500', label: '201-500 employees' },
  { value: '501-1000', label: '501-1000 employees' },
  { value: '1000+', label: '1000+ employees' },
];

// Countries (reused from CompanySignUpForm)
const countries = countryList.getData().map(country => ({
  value: country.code,
  label: country.name,
}));

const schema = z.object({
  companyName: z.string().min(2, 'Please enter your company name (at least 2 characters)').max(100, 'Company name is too long'),
  firstName: z.string().min(2, 'Please enter your first name (at least 2 characters)').max(50, 'First name is too long'),
  lastName: z.string().min(2, 'Please enter your last name (at least 2 characters)').max(50, 'Last name is too long'),
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  industry: z.string().min(1, 'Please select an industry'),
  companySize: z.string().min(1, 'Please select a company size'),
  country: z.string().min(1, 'Please select a country'),
});

const EditProfile = () => {
  const { t, i18n } = useTranslation();
  const lang = i18n.language || 'en';
  const isRTL = lang === 'ar';
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  
  const { register, handleSubmit, control, formState: { errors }, reset } = useForm({
    resolver: zodResolver(schema),
    mode: 'onChange',
    defaultValues: {
      companyName: 'Akhdar',
      firstName: 'Yasser',
      lastName: 'Mohamed',
      email: '<EMAIL>',
      password: '********',
      industry: 'technology',
      companySize: '11-50',
      country: 'EG',
    }
  });

  const onSubmit = (data) => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      toast.success("Profile updated successfully!", { position: "top-center" });
      console.log('Profile update data:', data);
    }, 1200);
  };

  return (
    <>
      <Toaster position="top-center" />
      <SettingsLayout title="Edit Your Personal Profile" backTo="/dashboard">
        <div className="mb-4">
          <h2 className="text-lg font-bold mb-6 text-primary text-center">Edit Profile</h2>
          
          <form onSubmit={handleSubmit(onSubmit)} className="w-full">
            <div className="mb-4">
              <Input 
                label="Company Name" 
                placeholder="Company Name" 
                {...register('companyName')} 
                aria-invalid={!!errors.companyName} 
              />
              <ErrorMessage message={errors.companyName?.message} />
            </div>
            
            <div className="flex gap-4">
              <div className="flex-1 mb-3">
                <Input 
                  label="First Name" 
                  placeholder="First Name" 
                  {...register('firstName')} 
                  aria-invalid={!!errors.firstName} 
                />
                <ErrorMessage message={errors.firstName?.message} />
              </div>
              <div className="flex-1 mb-3">
                <Input 
                  label="Last Name" 
                  placeholder="Last Name" 
                  {...register('lastName')} 
                  aria-invalid={!!errors.lastName} 
                />
                <ErrorMessage message={errors.lastName?.message} />
              </div>
            </div>
            
            <div className="mb-3">
              <label className="block mb-1 text-[16px] font-semibold text-primary">Password</label>
              <div className="relative">
                <Input
                  placeholder="Password"
                  type={showPassword ? 'text' : 'password'}
                  {...register('password')}
                  aria-invalid={!!errors.password}
                />
                <button
                  type="button"
                  tabIndex={-1}
                  className={`absolute ${isRTL ? 'left-3' : 'right-3'} top-1/2 -translate-y-1/2 text-gray-400 cursor-pointer`}
                  onClick={() => setShowPassword(v => !v)}
                >
                  {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                </button>
              </div>
              <ErrorMessage message={errors.password?.message} />
            </div>
            
            <div className="mb-3">
              <Input 
                label="Email" 
                placeholder="Email" 
                type="email" 
                {...register('email')} 
                aria-invalid={!!errors.email} 
              />
              <ErrorMessage message={errors.email?.message} />
            </div>
            
            <div className="flex gap-4 mt-2 flex-col sm:flex-row">
              <div className="w-full mb-3">
                <label className="block mb-1 text-[16px] font-semibold text-primary">Industry</label>
                <Controller
                  name="industry"
                  control={control}
                  render={({ field }) => (
                    <Select value={field.value} onValueChange={field.onChange}>
                      <SelectTrigger className="w-full bg-[#F0F5EF]">
                        <SelectValue placeholder="Select industry" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          <SelectLabel>Industry</SelectLabel>
                          {industries.map((option) => (
                            <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                          ))}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  )}
                />
                <ErrorMessage message={errors.industry?.message} />
              </div>
              
              <div className="w-full mb-3">
                <label className="block mb-1 text-[16px] font-semibold text-primary">Company Size</label>
                <Controller
                  name="companySize"
                  control={control}
                  render={({ field }) => (
                    <Select value={field.value} onValueChange={field.onChange}>
                      <SelectTrigger className="w-full bg-[#F0F5EF]">
                        <SelectValue placeholder="Select size" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          <SelectLabel>Company Size</SelectLabel>
                          {companySizes.map((option) => (
                            <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                          ))}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  )}
                />
                <ErrorMessage message={errors.companySize?.message} />
              </div>
            </div>
            
            <div className="mt-2 mb-3">
              <label className="block mb-1 text-[16px] font-semibold text-primary">4- Country</label>
              <Controller
                name="country"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger className="w-full bg-[#F0F5EF]">
                      <SelectValue placeholder="Select country" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        <SelectLabel>Country</SelectLabel>
                        {countries.map((option) => (
                          <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                        ))}
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                )}
              />
              <ErrorMessage message={errors.country?.message} />
            </div>
            
            <div className="flex justify-center mt-6">
              <button 
                type="submit" 
                disabled={loading} 
                className="py-2 px-16 rounded-xl bg-main text-black font-semibold hover:bg-primary hover:text-white transition cursor-pointer flex items-center justify-center min-w-[120px]"
              >
                {loading ? <span className="loader mr-2"></span> : null}
                {loading ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </form>
        </div>
      </SettingsLayout>
    </>
  );
};

export default EditProfile;
