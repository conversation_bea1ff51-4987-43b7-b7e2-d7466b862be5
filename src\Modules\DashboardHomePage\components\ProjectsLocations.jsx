import React from 'react';
import { projectsData, dashboardConfig } from '../data/mockData';

const ProjectsLocations = () => {
  const hasData = dashboardConfig.hasLocationData;

  if (!hasData) {
    // Empty state
    return (
      <section className=" p-8">
        <h2 className="text-3xl font-bold text-primary mb-8 text-center">Projects Locations</h2>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
          {/* World Map Placeholder */}
          <div className=" rounded-lg p-8 h-80 flex items-center justify-center">
            <div className="text-center">
              <div className="w-full h-full bg-blue-200 rounded-lg mb-4 flex items-center justify-center">
                <span className="text-blue-600 text-6xl">🗺️</span>
              </div>
            </div>
          </div>
          
          {/* Empty State Message */}
          <div className="text-center lg:text-left">
            <h3 className="text-xl font-semibold text-primary mb-4">
              Hey! You have no projects yet.
            </h3>
            <p className="text-gray-600 mb-6">
              Click below to add projects
            </p>
            <button className="bg-white border border-gray-300 text-primary px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors">
              Marketplace
            </button>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className=" p-8">
      <h2 className="text-3xl font-bold text-primary mb-8 text-center">Projects Locations</h2>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {/* World Map */}
        <div className="rounded-lg overflow-hidden">
          <iframe
            src="https://www.google.com/maps/embed?pb=!1m14!1m12!1m3!1d127748.83149344707!2d-0.1276!3d51.5074!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!5e0!3m2!1sen!2suk!4v1234567890"
            width="100%"
            height="320"
            style={{ border: 0 }}
            allowFullScreen=""
            loading="lazy"
            referrerPolicy="no-referrer-when-downgrade"
            className="rounded-lg"
          ></iframe>
        </div>

        {/* Your Projects List */}
        <div>
          <h3 className="text-xl font-semibold text-primary mb-6">Your Projects</h3>

          <div className="space-y-4 max-h-80 overflow-y-auto">
            {projectsData.map((project) => (
              <div key={project.id} className="bg-gray-800 text-white rounded-lg p-4 relative overflow-hidden">
                {/* Background image/gradient */}
                <div className="absolute inset-0 bg-gradient-to-r from-green-600 to-green-400 opacity-90"></div>

                <div className="relative z-10 flex justify-between items-center">
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <span className="w-2 h-2 bg-white rounded-full"></span>
                      <span className="text-sm">{project.type}</span>
                    </div>
                    <h4 className="font-semibold text-lg">{project.title}</h4>
                  </div>

                  <div className="text-right">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-lg">{project.flag}</span>
                      <span className="text-sm">{project.location}</span>
                    </div>
                    <div className="flex items-center gap-1 mt-2">
                      <span className="w-4 h-4 bg-white rounded-full flex items-center justify-center">
                        <span className="text-xs text-gray-800">👥</span>
                      </span>
                      <span className="text-xs">3</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProjectsLocations;
